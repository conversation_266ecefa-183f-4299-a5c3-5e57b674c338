# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_WS_URL=http://localhost:5000

# Application Configuration
NEXT_PUBLIC_APP_NAME=TicketFlow AI
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_DESCRIPTION=Customer Support Automation System

# Feature Flags
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_DARK_MODE=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true

# External Services
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_google_analytics_id
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# Development Configuration
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_MOCK_API=false

# UI Configuration
NEXT_PUBLIC_DEFAULT_THEME=light
NEXT_PUBLIC_DEFAULT_LANGUAGE=en
NEXT_PUBLIC_ITEMS_PER_PAGE=20

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/plain

# Social Media Links (Optional)
NEXT_PUBLIC_GITHUB_URL=https://github.com/HectorTa1989/customer-support-automation
NEXT_PUBLIC_LINKEDIN_URL=https://linkedin.com/in/hectortapia
NEXT_PUBLIC_TWITTER_URL=https://twitter.com/hectortapia

# Support Configuration
NEXT_PUBLIC_SUPPORT_EMAIL=<EMAIL>
NEXT_PUBLIC_SUPPORT_PHONE=******-123-4567
NEXT_PUBLIC_DOCUMENTATION_URL=https://docs.ticketflow.ai
