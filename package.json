{"name": "customer-support-automation", "version": "1.0.0", "description": "Comprehensive Customer Support Ticket Automation System with AI-powered routing and intelligent responses", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "test": "npm run test:server && npm run test:client", "test:server": "cd server && npm test", "test:client": "cd client && npm test", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "setup": "npm run install:all && npm run db:setup", "db:setup": "cd server && npm run db:migrate && npm run db:seed"}, "keywords": ["customer-support", "ticket-automation", "ai-routing", "helpdesk", "sla-monitoring", "sentiment-analysis"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/customer-support-automation"}, "devDependencies": {"concurrently": "^8.2.2"}}