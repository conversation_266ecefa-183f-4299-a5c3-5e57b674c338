const nodemailer = require('nodemailer');
const logger = require('../utils/logger');
const database = require('../config/database');
const redis = require('../config/redis');

class NotificationService {
  constructor() {
    this.emailTransporter = null;
    this.io = null;
    this.initialized = false;
  }

  initialize(socketIO) {
    try {
      this.io = socketIO;

      // Initialize email transporter
      this.emailTransporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: process.env.SMTP_PORT || 587,
        secure: false,
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });

      // Subscribe to Redis channels for distributed notifications
      this.subscribeToChannels();

      this.initialized = true;
      logger.info('Notification service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize notification service:', error);
      throw error;
    }
  }

  async subscribeToChannels() {
    try {
      // Subscribe to notification channels
      await redis.subscribe('notifications', (message) => {
        this.handleDistributedNotification(message);
      });

      await redis.subscribe('email_queue', (message) => {
        this.processEmailQueue(message);
      });

      logger.info('Subscribed to notification channels');
    } catch (error) {
      logger.error('Error subscribing to notification channels:', error);
    }
  }

  async sendNotification(notificationData) {
    try {
      const { userId, type, title, message, data = {}, channels = ['database', 'realtime'] } = notificationData;

      // Store notification in database
      if (channels.includes('database')) {
        await this.storeNotification({
          user_id: userId,
          type,
          title,
          message,
          data
        });
      }

      // Send real-time notification
      if (channels.includes('realtime') && this.io) {
        await this.sendRealtimeNotification(userId, {
          type,
          title,
          message,
          data,
          timestamp: new Date().toISOString()
        });
      }

      // Send email notification
      if (channels.includes('email')) {
        await this.queueEmailNotification(userId, {
          type,
          title,
          message,
          data
        });
      }

      // Send SMS notification (if configured)
      if (channels.includes('sms')) {
        await this.queueSMSNotification(userId, {
          type,
          title,
          message,
          data
        });
      }

      logger.debug(`Notification sent to user ${userId}: ${title}`);
    } catch (error) {
      logger.error('Error sending notification:', error);
      throw error;
    }
  }

  async storeNotification(notificationData) {
    try {
      await database.create('notifications', notificationData);
    } catch (error) {
      logger.error('Error storing notification:', error);
      throw error;
    }
  }

  async sendRealtimeNotification(userId, notification) {
    try {
      if (!this.io) {
        logger.warn('Socket.IO not initialized, skipping real-time notification');
        return;
      }

      // Send to user's personal room
      this.io.to(`user_${userId}`).emit('notification', notification);

      // Also publish to Redis for distributed systems
      await redis.publish('notifications', {
        userId,
        notification
      });

      logger.debug(`Real-time notification sent to user ${userId}`);
    } catch (error) {
      logger.error('Error sending real-time notification:', error);
    }
  }

  async queueEmailNotification(userId, notificationData) {
    try {
      // Get user email
      const user = await database.findById('users', userId);
      if (!user || !user.email) {
        logger.warn(`No email found for user ${userId}`);
        return;
      }

      const emailData = {
        to: user.email,
        subject: notificationData.title,
        type: notificationData.type,
        data: {
          ...notificationData.data,
          user_name: `${user.first_name} ${user.last_name}`,
          message: notificationData.message
        }
      };

      // Queue email for processing
      await redis.lpush('email_queue', emailData);
      
      // Also publish to Redis channel for immediate processing
      await redis.publish('email_queue', emailData);

      logger.debug(`Email notification queued for ${user.email}`);
    } catch (error) {
      logger.error('Error queuing email notification:', error);
    }
  }

  async processEmailQueue(emailData) {
    try {
      if (!this.emailTransporter) {
        logger.warn('Email transporter not initialized');
        return;
      }

      const { to, subject, type, data } = emailData;

      // Generate email content based on type
      const emailContent = await this.generateEmailContent(type, data);

      const mailOptions = {
        from: process.env.SMTP_FROM || '<EMAIL>',
        to,
        subject,
        html: emailContent.html,
        text: emailContent.text
      };

      await this.emailTransporter.sendMail(mailOptions);
      logger.info(`Email sent successfully to ${to}`);
    } catch (error) {
      logger.error('Error processing email queue:', error);
      
      // Retry logic could be implemented here
      // For now, we'll just log the error
    }
  }

  async generateEmailContent(type, data) {
    try {
      const templates = {
        ticket_created: {
          html: `
            <h2>New Support Ticket Created</h2>
            <p>Hello ${data.user_name},</p>
            <p>Your support ticket has been created successfully.</p>
            <p><strong>Ticket Number:</strong> ${data.ticket_number}</p>
            <p><strong>Subject:</strong> ${data.title}</p>
            <p><strong>Priority:</strong> ${data.priority}</p>
            <p>We will respond to your ticket as soon as possible.</p>
            <p>Best regards,<br>Support Team</p>
          `,
          text: `New Support Ticket Created\n\nHello ${data.user_name},\n\nYour support ticket has been created successfully.\n\nTicket Number: ${data.ticket_number}\nSubject: ${data.title}\nPriority: ${data.priority}\n\nWe will respond to your ticket as soon as possible.\n\nBest regards,\nSupport Team`
        },
        ticket_updated: {
          html: `
            <h2>Support Ticket Updated</h2>
            <p>Hello ${data.user_name},</p>
            <p>Your support ticket has been updated.</p>
            <p><strong>Ticket Number:</strong> ${data.ticket_number}</p>
            <p><strong>Status:</strong> ${data.status}</p>
            <p><strong>Update:</strong> ${data.message}</p>
            <p>Best regards,<br>Support Team</p>
          `,
          text: `Support Ticket Updated\n\nHello ${data.user_name},\n\nYour support ticket has been updated.\n\nTicket Number: ${data.ticket_number}\nStatus: ${data.status}\nUpdate: ${data.message}\n\nBest regards,\nSupport Team`
        },
        sla_warning: {
          html: `
            <h2>SLA Breach Warning</h2>
            <p>Hello,</p>
            <p>This is a warning that ticket #${data.ticket_number} is approaching SLA breach.</p>
            <p><strong>Time Remaining:</strong> ${data.minutes_remaining} minutes</p>
            <p><strong>Customer:</strong> ${data.customer_name}</p>
            <p><strong>Priority:</strong> ${data.priority}</p>
            <p>Please take immediate action.</p>
          `,
          text: `SLA Breach Warning\n\nThis is a warning that ticket #${data.ticket_number} is approaching SLA breach.\n\nTime Remaining: ${data.minutes_remaining} minutes\nCustomer: ${data.customer_name}\nPriority: ${data.priority}\n\nPlease take immediate action.`
        },
        satisfaction_survey: {
          html: `
            <h2>How was your support experience?</h2>
            <p>Hello ${data.user_name},</p>
            <p>We hope your issue has been resolved satisfactorily.</p>
            <p>Please take a moment to rate your experience:</p>
            <p><a href="${data.survey_link}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Take Survey</a></p>
            <p>Your feedback helps us improve our service.</p>
            <p>Best regards,<br>Support Team</p>
          `,
          text: `How was your support experience?\n\nHello ${data.user_name},\n\nWe hope your issue has been resolved satisfactorily.\n\nPlease take a moment to rate your experience: ${data.survey_link}\n\nYour feedback helps us improve our service.\n\nBest regards,\nSupport Team`
        }
      };

      return templates[type] || {
        html: `<p>${data.message}</p>`,
        text: data.message
      };
    } catch (error) {
      logger.error('Error generating email content:', error);
      return {
        html: `<p>${data.message}</p>`,
        text: data.message
      };
    }
  }

  async queueSMSNotification(userId, notificationData) {
    try {
      // Get user phone number
      const user = await database.findById('users', userId);
      if (!user || !user.phone) {
        logger.warn(`No phone number found for user ${userId}`);
        return;
      }

      const smsData = {
        to: user.phone,
        message: `${notificationData.title}: ${notificationData.message}`,
        type: notificationData.type,
        data: notificationData.data
      };

      // Queue SMS for processing
      await redis.lpush('sms_queue', smsData);

      logger.debug(`SMS notification queued for ${user.phone}`);
    } catch (error) {
      logger.error('Error queuing SMS notification:', error);
    }
  }

  async handleDistributedNotification(message) {
    try {
      const { userId, notification } = message;
      
      // Send real-time notification to connected clients
      if (this.io) {
        this.io.to(`user_${userId}`).emit('notification', notification);
      }
    } catch (error) {
      logger.error('Error handling distributed notification:', error);
    }
  }

  async getNotifications(userId, options = {}) {
    try {
      const { limit = 20, offset = 0, unreadOnly = false } = options;

      const conditions = { user_id: userId };
      if (unreadOnly) {
        conditions.is_read = false;
      }

      const notifications = await database.findMany('notifications', conditions, {
        orderBy: 'created_at',
        orderDirection: 'DESC',
        limit,
        offset
      });

      return notifications;
    } catch (error) {
      logger.error('Error getting notifications:', error);
      return [];
    }
  }

  async markAsRead(notificationId, userId) {
    try {
      // Verify notification belongs to user
      const notification = await database.findById('notifications', notificationId);
      if (!notification || notification.user_id !== userId) {
        throw new Error('Notification not found or access denied');
      }

      await database.update('notifications', notificationId, { is_read: true });
      return true;
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async markAllAsRead(userId) {
    try {
      const query = `
        UPDATE notifications 
        SET is_read = true, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1 AND is_read = false
      `;
      
      await database.query(query, [userId]);
      return true;
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  async getUnreadCount(userId) {
    try {
      return await database.count('notifications', {
        user_id: userId,
        is_read: false
      });
    } catch (error) {
      logger.error('Error getting unread count:', error);
      return 0;
    }
  }
}

module.exports = new NotificationService();
