const cron = require('node-cron');
const logger = require('../utils/logger');
const database = require('../config/database');
const redis = require('../config/redis');
const notificationService = require('./notificationService');

class SLAService {
  constructor() {
    this.monitoringActive = false;
    this.checkInterval = '*/5 * * * *'; // Every 5 minutes
  }

  startMonitoring() {
    if (this.monitoringActive) {
      logger.warn('SLA monitoring is already active');
      return;
    }

    // Schedule SLA monitoring job
    cron.schedule(this.checkInterval, async () => {
      await this.checkSLABreaches();
    });

    this.monitoringActive = true;
    logger.info('SLA monitoring service started');
  }

  stopMonitoring() {
    this.monitoringActive = false;
    logger.info('SLA monitoring service stopped');
  }

  async checkSLABreaches() {
    try {
      logger.debug('Checking for SLA breaches...');

      // Get tickets approaching SLA breach (within 30 minutes)
      const approachingBreaches = await this.getApproachingBreaches();
      
      // Get tickets that have already breached SLA
      const currentBreaches = await this.getCurrentBreaches();

      // Process approaching breaches
      for (const ticket of approachingBreaches) {
        await this.handleApproachingBreach(ticket);
      }

      // Process current breaches
      for (const ticket of currentBreaches) {
        await this.handleSLABreach(ticket);
      }

      logger.debug(`SLA check completed. Approaching: ${approachingBreaches.length}, Breached: ${currentBreaches.length}`);
    } catch (error) {
      logger.error('Error checking SLA breaches:', error);
    }
  }

  async getApproachingBreaches() {
    try {
      const query = `
        SELECT t.*, cp.tier, u.email as customer_email, u.first_name, u.last_name
        FROM tickets t
        JOIN users u ON t.customer_id = u.id
        LEFT JOIN customer_profiles cp ON u.id = cp.user_id
        WHERE t.status IN ('open', 'in_progress')
        AND t.sla_due_at IS NOT NULL
        AND t.sla_due_at > NOW()
        AND t.sla_due_at <= NOW() + INTERVAL '30 minutes'
        AND NOT EXISTS (
          SELECT 1 FROM notifications n 
          WHERE n.user_id = t.assigned_agent_id 
          AND n.type = 'sla_warning'
          AND n.data->>'ticket_id' = t.id::text
          AND n.created_at > NOW() - INTERVAL '1 hour'
        )
      `;

      const result = await database.query(query);
      return result.rows;
    } catch (error) {
      logger.error('Error getting approaching SLA breaches:', error);
      return [];
    }
  }

  async getCurrentBreaches() {
    try {
      const query = `
        SELECT t.*, cp.tier, u.email as customer_email, u.first_name, u.last_name,
               au.email as agent_email, au.first_name as agent_first_name
        FROM tickets t
        JOIN users u ON t.customer_id = u.id
        LEFT JOIN customer_profiles cp ON u.id = cp.user_id
        LEFT JOIN users au ON t.assigned_agent_id = au.id
        WHERE t.status IN ('open', 'in_progress')
        AND t.sla_due_at IS NOT NULL
        AND t.sla_due_at < NOW()
      `;

      const result = await database.query(query);
      return result.rows;
    } catch (error) {
      logger.error('Error getting current SLA breaches:', error);
      return [];
    }
  }

  async handleApproachingBreach(ticket) {
    try {
      const timeRemaining = new Date(ticket.sla_due_at) - new Date();
      const minutesRemaining = Math.floor(timeRemaining / (1000 * 60));

      // Notify assigned agent
      if (ticket.assigned_agent_id) {
        await notificationService.sendNotification({
          userId: ticket.assigned_agent_id,
          type: 'sla_warning',
          title: 'SLA Breach Warning',
          message: `Ticket #${ticket.ticket_number} will breach SLA in ${minutesRemaining} minutes`,
          data: {
            ticket_id: ticket.id,
            ticket_number: ticket.ticket_number,
            minutes_remaining: minutesRemaining,
            customer_name: `${ticket.first_name} ${ticket.last_name}`,
            priority: ticket.priority
          }
        });
      }

      // Notify managers for high priority tickets
      if (ticket.priority === 'high' || ticket.priority === 'critical') {
        const managers = await this.getManagersForDepartment(ticket.category);
        for (const manager of managers) {
          await notificationService.sendNotification({
            userId: manager.id,
            type: 'sla_warning_manager',
            title: 'High Priority SLA Warning',
            message: `High priority ticket #${ticket.ticket_number} approaching SLA breach`,
            data: {
              ticket_id: ticket.id,
              ticket_number: ticket.ticket_number,
              minutes_remaining: minutesRemaining,
              assigned_agent: ticket.agent_email,
              customer_tier: ticket.tier
            }
          });
        }
      }

      // Cache the warning to prevent spam
      await redis.set(`sla_warning:${ticket.id}`, true, 3600);

      logger.info(`SLA warning sent for ticket ${ticket.ticket_number}`);
    } catch (error) {
      logger.error('Error handling approaching SLA breach:', error);
    }
  }

  async handleSLABreach(ticket) {
    try {
      const breachTime = new Date() - new Date(ticket.sla_due_at);
      const minutesBreached = Math.floor(breachTime / (1000 * 60));

      // Update ticket with SLA breach flag
      await database.update('tickets', ticket.id, {
        metadata: {
          ...ticket.metadata,
          sla_breached: true,
          breach_time: new Date().toISOString(),
          minutes_breached: minutesBreached
        }
      });

      // Escalate ticket based on breach severity
      await this.escalateBreachedTicket(ticket, minutesBreached);

      // Record SLA breach metric
      await this.recordSLABreach(ticket, minutesBreached);

      logger.warn(`SLA breach detected for ticket ${ticket.ticket_number} (${minutesBreached} minutes)`);
    } catch (error) {
      logger.error('Error handling SLA breach:', error);
    }
  }

  async escalateBreachedTicket(ticket, minutesBreached) {
    try {
      // Get escalation rules
      const escalationRules = await this.getEscalationRules(ticket);

      for (const rule of escalationRules) {
        if (this.shouldEscalate(ticket, rule, minutesBreached)) {
          await this.executeEscalation(ticket, rule);
        }
      }
    } catch (error) {
      logger.error('Error escalating breached ticket:', error);
    }
  }

  async getEscalationRules(ticket) {
    try {
      const query = `
        SELECT * FROM escalation_rules
        WHERE is_active = true
        ORDER BY priority ASC
      `;

      const result = await database.query(query);
      return result.rows.filter(rule => {
        // Check if rule conditions match the ticket
        const conditions = rule.conditions;
        return this.matchesConditions(ticket, conditions);
      });
    } catch (error) {
      logger.error('Error getting escalation rules:', error);
      return [];
    }
  }

  matchesConditions(ticket, conditions) {
    try {
      // Check priority condition
      if (conditions.priority && !conditions.priority.includes(ticket.priority)) {
        return false;
      }

      // Check category condition
      if (conditions.category && !conditions.category.includes(ticket.category)) {
        return false;
      }

      // Check customer tier condition
      if (conditions.customer_tier && !conditions.customer_tier.includes(ticket.tier)) {
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error matching escalation conditions:', error);
      return false;
    }
  }

  shouldEscalate(ticket, rule, minutesBreached) {
    const conditions = rule.conditions;
    
    // Check if breach time threshold is met
    if (conditions.breach_threshold_minutes && minutesBreached < conditions.breach_threshold_minutes) {
      return false;
    }

    return true;
  }

  async executeEscalation(ticket, rule) {
    try {
      const actions = rule.actions;

      // Reassign to manager
      if (actions.reassign_to_manager) {
        const managers = await this.getManagersForDepartment(ticket.category);
        if (managers.length > 0) {
          const manager = managers[0]; // Assign to first available manager
          await database.update('tickets', ticket.id, {
            assigned_agent_id: manager.id,
            priority: actions.increase_priority ? 'critical' : ticket.priority
          });

          await notificationService.sendNotification({
            userId: manager.id,
            type: 'ticket_escalated',
            title: 'Ticket Escalated Due to SLA Breach',
            message: `Ticket #${ticket.ticket_number} has been escalated to you due to SLA breach`,
            data: {
              ticket_id: ticket.id,
              ticket_number: ticket.ticket_number,
              original_agent: ticket.assigned_agent_id,
              escalation_reason: 'SLA breach'
            }
          });
        }
      }

      // Send email notification
      if (actions.send_email) {
        // Implementation would depend on email service
        logger.info(`Email escalation triggered for ticket ${ticket.ticket_number}`);
      }

      // Create internal note
      if (actions.create_note) {
        await database.create('ticket_messages', {
          ticket_id: ticket.id,
          sender_id: null, // System message
          message_type: 'system',
          content: `Ticket automatically escalated due to SLA breach. Rule: ${rule.name}`,
          is_internal: true
        });
      }

      logger.info(`Escalation executed for ticket ${ticket.ticket_number} using rule: ${rule.name}`);
    } catch (error) {
      logger.error('Error executing escalation:', error);
    }
  }

  async getManagersForDepartment(category) {
    try {
      const query = `
        SELECT u.* FROM users u
        JOIN agent_profiles ap ON u.id = ap.user_id
        WHERE u.role = 'manager'
        AND ap.department = $1
        AND u.is_active = true
      `;

      const result = await database.query(query, [category]);
      return result.rows;
    } catch (error) {
      logger.error('Error getting managers for department:', error);
      return [];
    }
  }

  async recordSLABreach(ticket, minutesBreached) {
    try {
      await database.create('ticket_metrics', {
        ticket_id: ticket.id,
        metric_name: 'sla_breach',
        metric_value: minutesBreached,
        metric_unit: 'minutes'
      });
    } catch (error) {
      logger.error('Error recording SLA breach metric:', error);
    }
  }

  async calculateSLA(ticketData) {
    try {
      const { customer_tier, priority, category } = ticketData;

      // Get SLA policy
      const query = `
        SELECT * FROM sla_policies
        WHERE customer_tier = $1 AND priority = $2 AND is_active = true
        ORDER BY created_at DESC
        LIMIT 1
      `;

      const result = await database.query(query, [customer_tier || 'standard', priority]);
      
      if (result.rows.length === 0) {
        // Default SLA if no policy found
        return {
          first_response_time: 60, // 1 hour
          resolution_time: 1440, // 24 hours
          escalation_time: 120 // 2 hours
        };
      }

      return result.rows[0];
    } catch (error) {
      logger.error('Error calculating SLA:', error);
      return {
        first_response_time: 60,
        resolution_time: 1440,
        escalation_time: 120
      };
    }
  }

  async setSLADueDate(ticketId, slaPolicy) {
    try {
      const dueDate = new Date();
      dueDate.setMinutes(dueDate.getMinutes() + slaPolicy.first_response_time);

      await database.update('tickets', ticketId, {
        sla_due_at: dueDate.toISOString()
      });

      return dueDate;
    } catch (error) {
      logger.error('Error setting SLA due date:', error);
      throw error;
    }
  }
}

module.exports = new SLAService();
