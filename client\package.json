{"name": "ticketflow-ai-client", "version": "1.0.0", "description": "Frontend for TicketFlow AI Customer Support Automation System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "classnames": "^2.3.2", "framer-motion": "^10.16.16", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "react-table": "^7.8.0", "react-virtualized": "^9.22.5", "lodash": "^4.17.21", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "typescript": "^5.3.3", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "jest-environment-jsdom": "^29.7.0"}, "keywords": ["customer-support", "ticket-automation", "react", "nextjs", "dashboard"], "author": "HectorTa1989", "license": "MIT"}