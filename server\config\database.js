const { Pool } = require('pg');
const logger = require('../utils/logger');

class Database {
  constructor() {
    this.pool = null;
  }

  async connect() {
    try {
      this.pool = new Pool({
        user: process.env.DB_USER || 'postgres',
        host: process.env.DB_HOST || 'localhost',
        database: process.env.DB_NAME || 'ticketflow_ai',
        password: process.env.DB_PASSWORD || 'password',
        port: process.env.DB_PORT || 5432,
        max: 20, // maximum number of clients in the pool
        idleTimeoutMillis: 30000, // how long a client is allowed to remain idle
        connectionTimeoutMillis: 2000, // how long to wait for a connection
      });

      // Test the connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();

      logger.info('Database connection established successfully');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  async disconnect() {
    if (this.pool) {
      await this.pool.end();
      logger.info('Database connection closed');
    }
  }

  async query(text, params) {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      logger.debug('Executed query', { text, duration, rows: result.rowCount });
      return result;
    } catch (error) {
      logger.error('Database query error:', { text, error: error.message });
      throw error;
    }
  }

  async getClient() {
    return await this.pool.connect();
  }

  // Transaction helper
  async transaction(callback) {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Common query methods
  async findById(table, id) {
    const query = `SELECT * FROM ${table} WHERE id = $1`;
    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  async findByEmail(table, email) {
    const query = `SELECT * FROM ${table} WHERE email = $1`;
    const result = await this.query(query, [email]);
    return result.rows[0];
  }

  async create(table, data) {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const placeholders = keys.map((_, index) => `$${index + 1}`).join(', ');
    const columns = keys.join(', ');

    const query = `
      INSERT INTO ${table} (${columns})
      VALUES (${placeholders})
      RETURNING *
    `;

    const result = await this.query(query, values);
    return result.rows[0];
  }

  async update(table, id, data) {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const setClause = keys.map((key, index) => `${key} = $${index + 2}`).join(', ');

    const query = `
      UPDATE ${table}
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `;

    const result = await this.query(query, [id, ...values]);
    return result.rows[0];
  }

  async delete(table, id) {
    const query = `DELETE FROM ${table} WHERE id = $1 RETURNING *`;
    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  async findMany(table, conditions = {}, options = {}) {
    let query = `SELECT * FROM ${table}`;
    const values = [];
    let paramCount = 0;

    // Add WHERE conditions
    if (Object.keys(conditions).length > 0) {
      const whereClause = Object.keys(conditions).map(key => {
        paramCount++;
        values.push(conditions[key]);
        return `${key} = $${paramCount}`;
      }).join(' AND ');
      query += ` WHERE ${whereClause}`;
    }

    // Add ORDER BY
    if (options.orderBy) {
      query += ` ORDER BY ${options.orderBy}`;
      if (options.orderDirection) {
        query += ` ${options.orderDirection}`;
      }
    }

    // Add LIMIT and OFFSET
    if (options.limit) {
      paramCount++;
      values.push(options.limit);
      query += ` LIMIT $${paramCount}`;
    }

    if (options.offset) {
      paramCount++;
      values.push(options.offset);
      query += ` OFFSET $${paramCount}`;
    }

    const result = await this.query(query, values);
    return result.rows;
  }

  async count(table, conditions = {}) {
    let query = `SELECT COUNT(*) FROM ${table}`;
    const values = [];
    let paramCount = 0;

    if (Object.keys(conditions).length > 0) {
      const whereClause = Object.keys(conditions).map(key => {
        paramCount++;
        values.push(conditions[key]);
        return `${key} = $${paramCount}`;
      }).join(' AND ');
      query += ` WHERE ${whereClause}`;
    }

    const result = await this.query(query, values);
    return parseInt(result.rows[0].count);
  }

  // Search functionality
  async searchKnowledgeBase(searchTerm, limit = 10) {
    const query = `
      SELECT *, ts_rank(search_vector, plainto_tsquery($1)) as rank
      FROM knowledge_base
      WHERE search_vector @@ plainto_tsquery($1)
      AND is_published = true
      ORDER BY rank DESC
      LIMIT $2
    `;
    const result = await this.query(query, [searchTerm, limit]);
    return result.rows;
  }

  // Analytics queries
  async getTicketStats(startDate, endDate) {
    const query = `
      SELECT 
        COUNT(*) as total_tickets,
        COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_tickets,
        COUNT(CASE WHEN status = 'open' THEN 1 END) as open_tickets,
        AVG(EXTRACT(EPOCH FROM (resolved_at - created_at))/60) as avg_resolution_time
      FROM tickets
      WHERE created_at BETWEEN $1 AND $2
    `;
    const result = await this.query(query, [startDate, endDate]);
    return result.rows[0];
  }

  async getAgentPerformance(agentId, startDate, endDate) {
    const query = `
      SELECT 
        COUNT(*) as tickets_handled,
        AVG(EXTRACT(EPOCH FROM (first_response_at - created_at))/60) as avg_response_time,
        AVG(EXTRACT(EPOCH FROM (resolved_at - created_at))/60) as avg_resolution_time,
        COUNT(CASE WHEN resolved_at <= sla_due_at THEN 1 END) as sla_met,
        COUNT(*) as total_tickets
      FROM tickets
      WHERE assigned_agent_id = $1
      AND created_at BETWEEN $2 AND $3
      AND status = 'resolved'
    `;
    const result = await this.query(query, [agentId, startDate, endDate]);
    return result.rows[0];
  }
}

module.exports = new Database();
