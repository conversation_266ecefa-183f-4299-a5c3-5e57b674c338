# System Architecture Documentation

## High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React/Next.js Dashboard]
        B[Customer Portal]
        C[Agent Interface]
        D[Admin Panel]
    end
    
    subgraph "API Gateway"
        E[Express.js API Server]
        F[Authentication Middleware]
        G[Rate Limiting]
        H[Request Validation]
    end
    
    subgraph "Core Services"
        I[Ticket Management Service]
        J[AI Routing Engine]
        K[Auto-Response Generator]
        L[SLA Monitor]
        M[Notification Service]
        N[Analytics Engine]
    end
    
    subgraph "AI/ML Components"
        O[OpenAI GPT Integration]
        P[Sentiment Analysis]
        Q[Category Classifier]
        R[Knowledge Base Search]
        S[Priority Calculator]
    end
    
    subgraph "Data Layer"
        T[(PostgreSQL Database)]
        U[(Redis Cache)]
        V[(File Storage)]
        W[Search Index]
    end
    
    subgraph "External Services"
        X[Email Service]
        Y[SMS Gateway]
        Z[Webhook Endpoints]
        AA[Third-party APIs]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    G --> H
    H --> I
    
    I --> J
    I --> K
    I --> L
    I --> M
    I --> N
    
    J --> O
    J --> P
    J --> Q
    K --> O
    K --> R
    L --> S
    
    I --> T
    I --> U
    M --> X
    M --> Y
    N --> W
    
    I --> Z
    J --> AA
```

## Ticket Lifecycle Workflow

```mermaid
flowchart TD
    A[New Ticket Created] --> B{Source Type?}
    
    B -->|Email| C[Email Parser]
    B -->|Web Form| D[Form Processor]
    B -->|API| E[API Handler]
    B -->|Chat| F[Chat Processor]
    
    C --> G[Content Extraction]
    D --> G
    E --> G
    F --> G
    
    G --> H[AI Content Analysis]
    H --> I[Sentiment Analysis]
    H --> J[Category Classification]
    H --> K[Priority Calculation]
    
    I --> L{Sentiment Score}
    L -->|Negative| M[High Priority Flag]
    L -->|Neutral/Positive| N[Standard Processing]
    
    J --> O{Category Type}
    O -->|Technical| P[Tech Team Queue]
    O -->|Billing| Q[Billing Team Queue]
    O -->|General| R[General Support Queue]
    
    K --> S{Priority Level}
    S -->|Critical| T[Immediate Assignment]
    S -->|High| U[Priority Queue]
    S -->|Medium/Low| V[Standard Queue]
    
    M --> T
    N --> S
    
    T --> W[Agent Assignment]
    U --> W
    V --> W
    
    W --> X[Auto-Response Generation]
    X --> Y[Knowledge Base Search]
    Y --> Z[Send Initial Response]
    
    Z --> AA[SLA Timer Start]
    AA --> BB[Agent Works on Ticket]
    
    BB --> CC{Resolution Status}
    CC -->|Resolved| DD[Send Resolution]
    CC -->|Needs Escalation| EE[Escalation Process]
    CC -->|Pending Customer| FF[Awaiting Response]
    
    DD --> GG[Customer Satisfaction Survey]
    EE --> HH[Manager Assignment]
    FF --> II[Follow-up Timer]
    
    GG --> JJ[Ticket Closed]
    HH --> BB
    II --> KK{Response Received?}
    KK -->|Yes| BB
    KK -->|No| LL[Auto-Close Process]
    
    JJ --> MM[Analytics Update]
    LL --> MM
```

## AI Routing Decision Tree

```mermaid
flowchart TD
    A[Incoming Ticket] --> B[Extract Keywords & Context]
    B --> C[Sentiment Analysis]
    C --> D[Category Classification]
    
    D --> E{Technical Keywords?}
    E -->|Yes| F[Technical Category]
    E -->|No| G{Billing Keywords?}
    
    G -->|Yes| H[Billing Category]
    G -->|No| I{Account Keywords?}
    
    I -->|Yes| J[Account Management]
    I -->|No| K[General Inquiry]
    
    F --> L[Check Technical Complexity]
    H --> M[Check Billing Type]
    J --> N[Check Account Tier]
    K --> O[Standard Processing]
    
    L --> P{Complexity Level}
    P -->|High| Q[Senior Tech Agent]
    P -->|Medium| R[Standard Tech Agent]
    P -->|Low| S[Junior Tech Agent]
    
    M --> T{Billing Issue Type}
    T -->|Refund/Dispute| U[Billing Specialist]
    T -->|Payment| V[Payment Processor]
    T -->|Invoice| W[Billing Support]
    
    N --> X{Customer Tier}
    X -->|Premium| Y[Premium Support Agent]
    X -->|Standard| Z[Standard Agent]
    X -->|Basic| AA[Basic Support Queue]
    
    Q --> BB[High Priority Assignment]
    R --> CC[Medium Priority Assignment]
    S --> DD[Standard Assignment]
    U --> BB
    V --> CC
    W --> DD
    Y --> BB
    Z --> CC
    AA --> DD
    O --> DD
```
