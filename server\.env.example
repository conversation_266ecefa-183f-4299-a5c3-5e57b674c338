# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ticketflow_ai
DB_USER=postgres
DB_PASSWORD=your_database_password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_token_secret
JWT_REFRESH_EXPIRES_IN=30d

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>

# SMS Configuration (Optional - Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Application Configuration
NODE_ENV=development
PORT=5000
CLIENT_URL=http://localhost:3000

# File Upload Configuration
UPLOAD_MAX_SIZE=********
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/plain

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your_session_secret_key
SESSION_MAX_AGE=********

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# External API Keys (Optional)
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key
SENTIMENT_API_KEY=your_sentiment_analysis_api_key

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_for_external_integrations

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Monitoring and Analytics
ANALYTICS_ENABLED=true
ERROR_TRACKING_DSN=your_sentry_dsn_or_similar

# Feature Flags
ENABLE_AI_ROUTING=true
ENABLE_AUTO_RESPONSES=true
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_KNOWLEDGE_BASE_SEARCH=true
ENABLE_SLA_MONITORING=true

# Development Only
DEBUG_MODE=false
MOCK_EXTERNAL_APIS=false
