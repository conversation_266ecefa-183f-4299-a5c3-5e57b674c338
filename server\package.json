{"name": "ticketflow-ai-server", "version": "1.0.0", "description": "Backend API for TicketFlow AI Customer Support Automation System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "db:reset": "node scripts/reset.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "pg-pool": "^3.6.1", "redis": "^4.6.10", "ioredis": "^5.3.2", "socket.io": "^4.7.4", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "joi": "^17.11.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "openai": "^4.20.1", "natural": "^6.8.0", "sentiment": "^5.0.2", "node-cron": "^3.0.3", "axios": "^1.6.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "keywords": ["customer-support", "ticket-automation", "ai-routing", "express", "nodejs"], "author": "HectorTa1989", "license": "MIT"}