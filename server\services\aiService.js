const OpenAI = require('openai');
const natural = require('natural');
const Sentiment = require('sentiment');
const logger = require('../utils/logger');
const redis = require('../config/redis');
const database = require('../config/database');

class AIService {
  constructor() {
    this.openai = null;
    this.sentiment = new Sentiment();
    this.classifier = null;
    this.initialized = false;
  }

  async initialize() {
    try {
      // Initialize OpenAI
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Initialize and train the category classifier
      await this.initializeClassifier();

      this.initialized = true;
      logger.info('AI Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AI Service:', error);
      throw error;
    }
  }

  async initializeClassifier() {
    try {
      // Create a Naive Bayes classifier for ticket categorization
      this.classifier = new natural.BayesClassifier();

      // Training data for ticket categorization
      const trainingData = [
        // Technical issues
        { text: 'login error password reset authentication', category: 'technical' },
        { text: 'bug crash error 500 server down', category: 'technical' },
        { text: 'api integration webhook not working', category: 'technical' },
        { text: 'database connection timeout slow performance', category: 'technical' },
        { text: 'ssl certificate https security', category: 'technical' },
        
        // Billing issues
        { text: 'invoice payment billing charge refund', category: 'billing' },
        { text: 'subscription upgrade downgrade plan', category: 'billing' },
        { text: 'credit card declined payment failed', category: 'billing' },
        { text: 'receipt transaction money cost price', category: 'billing' },
        
        // Account management
        { text: 'account settings profile update change', category: 'account' },
        { text: 'user permissions access rights admin', category: 'account' },
        { text: 'delete account close subscription cancel', category: 'account' },
        { text: 'team members invite users organization', category: 'account' },
        
        // General inquiries
        { text: 'how to use tutorial guide help', category: 'general' },
        { text: 'feature request suggestion improvement', category: 'general' },
        { text: 'question about product information', category: 'general' },
        { text: 'demo trial evaluation testing', category: 'general' }
      ];

      // Train the classifier
      trainingData.forEach(item => {
        this.classifier.addDocument(item.text, item.category);
      });

      this.classifier.train();
      logger.info('Ticket classifier trained successfully');
    } catch (error) {
      logger.error('Failed to initialize classifier:', error);
      throw error;
    }
  }

  async analyzeTicket(ticketContent) {
    try {
      if (!this.initialized) {
        throw new Error('AI Service not initialized');
      }

      const analysis = {
        sentiment: this.analyzeSentiment(ticketContent),
        category: this.classifyTicket(ticketContent),
        priority: await this.calculatePriority(ticketContent),
        keywords: this.extractKeywords(ticketContent),
        confidence: 0.8 // Base confidence score
      };

      // Cache the analysis
      const cacheKey = `ticket_analysis:${Buffer.from(ticketContent).toString('base64').slice(0, 32)}`;
      await redis.set(cacheKey, analysis, 3600); // Cache for 1 hour

      return analysis;
    } catch (error) {
      logger.error('Error analyzing ticket:', error);
      throw error;
    }
  }

  analyzeSentiment(text) {
    try {
      const result = this.sentiment.analyze(text);
      
      // Normalize score to -1 to 1 range
      const normalizedScore = Math.max(-1, Math.min(1, result.score / 10));
      
      let sentiment = 'neutral';
      if (normalizedScore > 0.1) sentiment = 'positive';
      else if (normalizedScore < -0.1) sentiment = 'negative';

      return {
        score: normalizedScore,
        sentiment: sentiment,
        positive: result.positive,
        negative: result.negative
      };
    } catch (error) {
      logger.error('Error analyzing sentiment:', error);
      return { score: 0, sentiment: 'neutral', positive: [], negative: [] };
    }
  }

  classifyTicket(text) {
    try {
      const category = this.classifier.classify(text);
      const classifications = this.classifier.getClassifications(text);
      
      return {
        category: category,
        confidence: classifications[0]?.value || 0.5,
        allClassifications: classifications
      };
    } catch (error) {
      logger.error('Error classifying ticket:', error);
      return { category: 'general', confidence: 0.5, allClassifications: [] };
    }
  }

  async calculatePriority(text) {
    try {
      const urgentKeywords = ['urgent', 'critical', 'emergency', 'down', 'broken', 'not working', 'error', 'bug'];
      const highKeywords = ['important', 'asap', 'soon', 'problem', 'issue', 'help'];
      
      const lowerText = text.toLowerCase();
      
      let priorityScore = 1; // Default: low priority
      
      // Check for urgent keywords
      const urgentMatches = urgentKeywords.filter(keyword => lowerText.includes(keyword));
      if (urgentMatches.length > 0) {
        priorityScore = 4; // Critical
      } else {
        const highMatches = highKeywords.filter(keyword => lowerText.includes(keyword));
        if (highMatches.length > 0) {
          priorityScore = 3; // High
        } else if (lowerText.length > 500) {
          priorityScore = 2; // Medium (longer descriptions might indicate complex issues)
        }
      }

      const priorities = ['low', 'medium', 'high', 'critical'];
      return {
        level: priorities[priorityScore - 1],
        score: priorityScore,
        keywords: urgentMatches.concat(highMatches)
      };
    } catch (error) {
      logger.error('Error calculating priority:', error);
      return { level: 'medium', score: 2, keywords: [] };
    }
  }

  extractKeywords(text) {
    try {
      const tokenizer = new natural.WordTokenizer();
      const tokens = tokenizer.tokenize(text.toLowerCase());
      
      // Remove stop words and short words
      const stopWords = natural.stopwords;
      const keywords = tokens
        .filter(token => !stopWords.includes(token) && token.length > 2)
        .filter(token => /^[a-zA-Z]+$/.test(token)); // Only alphabetic words

      // Get word frequency
      const frequency = {};
      keywords.forEach(word => {
        frequency[word] = (frequency[word] || 0) + 1;
      });

      // Sort by frequency and return top keywords
      return Object.entries(frequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([word, count]) => ({ word, count }));
    } catch (error) {
      logger.error('Error extracting keywords:', error);
      return [];
    }
  }

  async generateAutoResponse(ticketData) {
    try {
      if (!this.openai) {
        throw new Error('OpenAI not initialized');
      }

      const { title, description, category, sentiment } = ticketData;

      // Check cache first
      const cacheKey = `auto_response:${category}:${sentiment.sentiment}`;
      const cachedResponse = await redis.get(cacheKey);
      if (cachedResponse) {
        return cachedResponse;
      }

      const prompt = `
        Generate a professional, empathetic customer support response for the following ticket:
        
        Category: ${category}
        Sentiment: ${sentiment.sentiment}
        Title: ${title}
        Description: ${description}
        
        The response should:
        1. Acknowledge the customer's issue
        2. Show empathy if the sentiment is negative
        3. Provide initial guidance or next steps
        4. Be professional but friendly
        5. Be concise (under 200 words)
        
        Response:
      `;

      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "You are a helpful customer support agent. Generate professional, empathetic responses to customer inquiries."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 300,
        temperature: 0.7,
      });

      const response = {
        content: completion.choices[0].message.content.trim(),
        generated_at: new Date().toISOString(),
        model: "gpt-3.5-turbo"
      };

      // Cache the response for similar tickets
      await redis.set(cacheKey, response, 7200); // Cache for 2 hours

      return response;
    } catch (error) {
      logger.error('Error generating auto response:', error);
      
      // Fallback response
      return {
        content: "Thank you for contacting our support team. We have received your request and will respond as soon as possible. Our team is committed to resolving your issue quickly and efficiently.",
        generated_at: new Date().toISOString(),
        model: "fallback"
      };
    }
  }

  async searchKnowledgeBase(query, limit = 5) {
    try {
      // First, try database search
      const dbResults = await database.searchKnowledgeBase(query, limit);
      
      if (dbResults.length > 0) {
        return dbResults.map(article => ({
          id: article.id,
          title: article.title,
          content: article.content.substring(0, 300) + '...',
          category: article.category,
          relevance: article.rank,
          source: 'database'
        }));
      }

      // If no results from database, use AI to generate suggestions
      if (this.openai) {
        const prompt = `
          Based on the query: "${query}"
          
          Suggest 3 relevant help topics that a customer support knowledge base should contain.
          Format as JSON array with title and brief description for each topic.
        `;

        const completion = await this.openai.chat.completions.create({
          model: "gpt-3.5-turbo",
          messages: [{ role: "user", content: prompt }],
          max_tokens: 400,
          temperature: 0.5,
        });

        try {
          const suggestions = JSON.parse(completion.choices[0].message.content);
          return suggestions.map((item, index) => ({
            id: `ai-${index}`,
            title: item.title,
            content: item.description,
            category: 'general',
            relevance: 0.8 - (index * 0.1),
            source: 'ai-generated'
          }));
        } catch (parseError) {
          logger.error('Error parsing AI knowledge base suggestions:', parseError);
        }
      }

      return [];
    } catch (error) {
      logger.error('Error searching knowledge base:', error);
      return [];
    }
  }

  async routeTicket(ticketData) {
    try {
      const { category, priority, sentiment, customer_tier } = ticketData;

      // Define routing rules
      const routingRules = {
        technical: {
          high: 'senior_tech_agent',
          medium: 'tech_agent',
          low: 'junior_tech_agent'
        },
        billing: {
          high: 'billing_specialist',
          medium: 'billing_agent',
          low: 'billing_agent'
        },
        account: {
          premium: 'premium_support',
          standard: 'standard_support',
          basic: 'basic_support'
        },
        general: {
          default: 'general_support'
        }
      };

      let assignmentType = 'general_support';

      // Route based on category and priority
      if (routingRules[category.category]) {
        if (category.category === 'account') {
          assignmentType = routingRules[category.category][customer_tier] || 'standard_support';
        } else {
          assignmentType = routingRules[category.category][priority.level] || 'standard_support';
        }
      }

      // Adjust for negative sentiment
      if (sentiment.sentiment === 'negative' && sentiment.score < -0.5) {
        if (assignmentType.includes('junior')) {
          assignmentType = assignmentType.replace('junior_', '');
        }
      }

      return {
        assignment_type: assignmentType,
        routing_reason: `Category: ${category.category}, Priority: ${priority.level}, Sentiment: ${sentiment.sentiment}`,
        confidence: category.confidence
      };
    } catch (error) {
      logger.error('Error routing ticket:', error);
      return {
        assignment_type: 'general_support',
        routing_reason: 'Default routing due to error',
        confidence: 0.5
      };
    }
  }
}

module.exports = new AIService();
