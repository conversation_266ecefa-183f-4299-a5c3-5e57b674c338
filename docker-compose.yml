version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ticketflow_postgres
    environment:
      POSTGRES_DB: ticketflow_ai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    networks:
      - ticketflow_network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ticketflow_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ticketflow_network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API Server
  api:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: ticketflow_api
    environment:
      NODE_ENV: production
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ticketflow_ai
      DB_USER: postgres
      DB_PASSWORD: postgres123
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your_jwt_secret_change_in_production
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      CLIENT_URL: http://localhost:3000
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    networks:
      - ticketflow_network
    restart: unless-stopped
    volumes:
      - ./server/logs:/app/logs
      - ./server/uploads:/app/uploads

  # Frontend Web Application
  web:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: ticketflow_web
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:5000
      NEXT_PUBLIC_WS_URL: http://localhost:5000
    ports:
      - "3000:3000"
    depends_on:
      - api
    networks:
      - ticketflow_network
    restart: unless-stopped

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: ticketflow_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - web
      - api
    networks:
      - ticketflow_network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  ticketflow_network:
    driver: bridge
