#!/bin/bash

# TicketFlow AI Deployment Script
# This script handles the deployment of the Customer Support Automation System

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ticketflow-ai"
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    mkdir -p logs backups server/logs server/uploads client/.next
    success "Directories created successfully"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if .env files exist
    if [ ! -f "server/.env" ]; then
        warning "server/.env file not found. Copying from example..."
        cp server/.env.example server/.env
        warning "Please edit server/.env with your configuration before continuing."
    fi
    
    if [ ! -f "client/.env.local" ]; then
        warning "client/.env.local file not found. Copying from example..."
        cp client/.env.example client/.env.local
        warning "Please edit client/.env.local with your configuration before continuing."
    fi
    
    success "Prerequisites check completed"
}

# Backup database
backup_database() {
    log "Creating database backup..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
    fi
    
    BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # Check if PostgreSQL container is running
    if docker-compose ps postgres | grep -q "Up"; then
        docker-compose exec -T postgres pg_dump -U postgres ticketflow_ai > "$BACKUP_FILE"
        success "Database backup created: $BACKUP_FILE"
    else
        warning "PostgreSQL container is not running. Skipping backup."
    fi
}

# Build and deploy
deploy() {
    log "Starting deployment process..."
    
    # Pull latest images
    log "Pulling latest Docker images..."
    docker-compose pull
    
    # Build custom images
    log "Building application images..."
    docker-compose build --no-cache
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose down
    
    # Start services
    log "Starting services..."
    docker-compose up -d
    
    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_health
    
    success "Deployment completed successfully!"
}

# Check service health
check_health() {
    log "Checking service health..."
    
    # Check PostgreSQL
    if docker-compose exec postgres pg_isready -U postgres > /dev/null 2>&1; then
        success "PostgreSQL is healthy"
    else
        error "PostgreSQL is not responding"
    fi
    
    # Check Redis
    if docker-compose exec redis redis-cli ping | grep -q "PONG"; then
        success "Redis is healthy"
    else
        error "Redis is not responding"
    fi
    
    # Check API server
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        success "API server is healthy"
    else
        error "API server is not responding"
    fi
    
    # Check web application
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        success "Web application is healthy"
    else
        error "Web application is not responding"
    fi
}

# Rollback function
rollback() {
    log "Starting rollback process..."
    
    # Stop current containers
    docker-compose down
    
    # Restore from backup if available
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/*.sql 2>/dev/null | head -n1)
    if [ -n "$LATEST_BACKUP" ]; then
        log "Restoring database from backup: $LATEST_BACKUP"
        docker-compose up -d postgres
        sleep 10
        docker-compose exec -T postgres psql -U postgres -d ticketflow_ai < "$LATEST_BACKUP"
    fi
    
    # Start services with previous version
    docker-compose up -d
    
    success "Rollback completed"
}

# Show logs
show_logs() {
    log "Showing application logs..."
    docker-compose logs -f --tail=100
}

# Clean up old images and containers
cleanup() {
    log "Cleaning up old Docker images and containers..."
    
    # Remove stopped containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove old backups (keep last 7 days)
    find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete 2>/dev/null || true
    
    success "Cleanup completed"
}

# Main script logic
case "${1:-deploy}" in
    "deploy")
        create_directories
        check_prerequisites
        backup_database
        deploy
        ;;
    "rollback")
        rollback
        ;;
    "logs")
        show_logs
        ;;
    "health")
        check_health
        ;;
    "cleanup")
        cleanup
        ;;
    "backup")
        backup_database
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|logs|health|cleanup|backup}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy the application (default)"
        echo "  rollback - Rollback to previous version"
        echo "  logs     - Show application logs"
        echo "  health   - Check service health"
        echo "  cleanup  - Clean up old Docker resources"
        echo "  backup   - Create database backup"
        exit 1
        ;;
esac
